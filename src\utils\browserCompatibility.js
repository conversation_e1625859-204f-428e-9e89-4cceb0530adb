// Browser compatibility utilities for the Online Voting System

/**
 * Check if the browser supports required features for the voting system
 */
export const checkBrowserCompatibility = () => {
  const compatibility = {
    webRTC: false,
    mediaDevices: false,
    canvas: false,
    webGL: false,
    localStorage: false,
    sessionStorage: false,
    webWorkers: false,
    overall: false
  };

  // Check WebRTC support
  compatibility.webRTC = !!(
    navigator.mediaDevices &&
    navigator.mediaDevices.getUserMedia &&
    window.RTCPeerConnection
  );

  // Check MediaDevices API
  compatibility.mediaDevices = !!(
    navigator.mediaDevices &&
    navigator.mediaDevices.getUserMedia
  );

  // Check Canvas support
  compatibility.canvas = !!(
    document.createElement('canvas').getContext &&
    document.createElement('canvas').getContext('2d')
  );

  // Check WebGL support
  try {
    const canvas = document.createElement('canvas');
    compatibility.webGL = !!(
      canvas.getContext('webgl') ||
      canvas.getContext('experimental-webgl')
    );
  } catch (e) {
    compatibility.webGL = false;
  }

  // Check localStorage support
  try {
    localStorage.setItem('test', 'test');
    localStorage.removeItem('test');
    compatibility.localStorage = true;
  } catch (e) {
    compatibility.localStorage = false;
  }

  // Check sessionStorage support
  try {
    sessionStorage.setItem('test', 'test');
    sessionStorage.removeItem('test');
    compatibility.sessionStorage = true;
  } catch (e) {
    compatibility.sessionStorage = false;
  }

  // Check Web Workers support
  compatibility.webWorkers = typeof Worker !== 'undefined';

  // Overall compatibility check
  compatibility.overall = 
    compatibility.webRTC &&
    compatibility.mediaDevices &&
    compatibility.canvas &&
    compatibility.localStorage &&
    compatibility.sessionStorage;

  return compatibility;
};

/**
 * Get browser information
 */
export const getBrowserInfo = () => {
  const userAgent = navigator.userAgent;
  let browserName = 'Unknown';
  let browserVersion = 'Unknown';

  if (userAgent.indexOf('Chrome') > -1) {
    browserName = 'Chrome';
    browserVersion = userAgent.match(/Chrome\/(\d+)/)?.[1] || 'Unknown';
  } else if (userAgent.indexOf('Firefox') > -1) {
    browserName = 'Firefox';
    browserVersion = userAgent.match(/Firefox\/(\d+)/)?.[1] || 'Unknown';
  } else if (userAgent.indexOf('Safari') > -1) {
    browserName = 'Safari';
    browserVersion = userAgent.match(/Version\/(\d+)/)?.[1] || 'Unknown';
  } else if (userAgent.indexOf('Edge') > -1) {
    browserName = 'Edge';
    browserVersion = userAgent.match(/Edge\/(\d+)/)?.[1] || 'Unknown';
  }

  return {
    name: browserName,
    version: browserVersion,
    userAgent: userAgent,
    platform: navigator.platform,
    language: navigator.language
  };
};

/**
 * Display compatibility warnings to the user
 */
export const displayCompatibilityWarnings = (compatibility) => {
  const warnings = [];

  if (!compatibility.webRTC) {
    warnings.push('WebRTC is not supported. Video verification features may not work.');
  }

  if (!compatibility.mediaDevices) {
    warnings.push('Media devices access is not supported. Camera and microphone features may not work.');
  }

  if (!compatibility.canvas) {
    warnings.push('Canvas is not supported. Some visual features may not work properly.');
  }

  if (!compatibility.localStorage) {
    warnings.push('Local storage is not supported. Some data may not persist between sessions.');
  }

  if (!compatibility.sessionStorage) {
    warnings.push('Session storage is not supported. Some temporary data may not be stored properly.');
  }

  return warnings;
};

/**
 * Check if the current browser is supported for secure voting
 */
export const isSecureVotingSupported = () => {
  const compatibility = checkBrowserCompatibility();
  return compatibility.overall;
};

// Export as browserCompatibility object
export const browserCompatibility = {
  checkBrowserCompatibility,
  getBrowserInfo,
  displayCompatibilityWarnings,
  isSecureVotingSupported
};

// Default export
const browserCompatibilityUtils = {
  checkBrowserCompatibility,
  getBrowserInfo,
  displayCompatibilityWarnings,
  isSecureVotingSupported
};

export default browserCompatibilityUtils;
